:root {
  /* palette  */
  --palette-autofillLight: #eef3fd;
  --palette-coal: #072252;
  --palette-coal_30: rgb(7, 34, 82, 0.3);
  --palette-lead: #4d719a;
  --palette-lead2: #9da2a4;
  --palette-trench: #3f73e3;
  --palette-chatBlue: #64a2ff;
  --palette-sky: #83b4ff;
  --palette-sky_30: rgb(131, 180, 255, 0.3);
  --palette-barBgLight: #cbdefc;
  --palette-brand: #5384ee;
  --palette-darkBrand: #314f8e;
  --palette-darkBrandHover: #21345f;
  --palette-brand_4: rgba(83, 132, 238, 0.04);
  --palette-brand_5: rgba(83, 132, 238, 0.05);
  --palette-brand_10: rgba(83, 132, 238, 0.1);
  --palette-busbrand_10: rgba(71, 101, 130, 0.1);
  --palette-brand_20: rgba(83, 132, 238, 0.2);
  --palette-brand_30: rgba(83, 132, 238, 0.3);
  --palette-brand_50: rgba(83, 132, 238, 0.5);
  --palette-brand_70: rgba(83, 132, 238, 0.7);
  --palette-white: #ffffff;
  --palette-white_10: rgba(255, 255, 255, 0.1);
  --palette-white_20: rgba(255, 255, 255, 0.2);
  --palette-white_40: rgba(255, 255, 255, 0.4);
  --palette-white_50: rgba(255, 255, 255, 0.5);
  --palette-white_60: rgba(255, 255, 255, 0.6);
  --palette-white_80: rgba(255, 255, 255, 0.8);
  --palette-white_90: rgba(255, 255, 255, 0.9);
  --palette-white_04: rgba(255, 255, 255, 0.04);
  --palette-wash-light: #e4e6eb;
  --palette-wash-dark: #3e4042;
  --palette-graphene: #485363;
  --palette-graphene_60: rgba(72, 83, 99, 0.6);
  --palette-highlightIcon: #586880;
  --palette-gray: #8493a8;
  --palette-gray_5: rgba(132, 147, 168, 0.05);
  --palette-gray_10: rgba(132, 147, 168, 0.1);
  --palette-gray_20: rgba(132, 147, 168, 0.2);
  --palette-gray_30: rgba(132, 147, 168, 0.3);
  --palette-gray_50: rgba(132, 147, 168, 0.5);
  --palette-gray_60: rgba(132, 147, 168, 0.6);
  --palette-gray_80: rgba(132, 147, 168, 0.8);
  --palette-gray_70: #e5e7ea;
  --palette-smoke: #ececec;
  --palette-smoke_30: rgb(236, 236, 236, 0.3);
  --palette-snow: #fcfcfc;
  --palette-snow_90: rgba(252, 252, 252, 0.9);
  --palette-blueFog: #f6fcff;
  --palette-arcticBlue: #dce9fc;
  --palette-sahra: #e5d1a9;
  --palette-elephantTusk: #fff6e4;
  --palette-cloud: #e1f5fe;
  --palette-chartPurple: #cdd2fd;
  --palette-verifiedUser: #00b0ff;
  --palette-success: #43a047;
  --palette-success_10: rgba(67, 160, 71, 0.1);
  --palette-success_20: rgba(67, 160, 71, 0.2);
  --palette-success_4: rgba(67, 160, 71, 0.04);
  --palette-success_5: rgba(67, 160, 71, 0.05);
  --palette-warning: #ff9a3e;
  --palette-irrelevant: #f47b61;
  --palette-irrelevant_10: rgba(244, 123, 97, 0.1);
  --palette-irrelevant_30: rgba(244, 123, 97, 0.3);
  --palette-warning_5: rgba(255, 140, 0, 0.05);
  --palette-warning_10: rgba(255, 154, 62, 0.1);
  --palette-warning_30: rgba(255, 154, 62, 0.3);
  --palette-error: #d32f2f;
  --palette-error_4: rgba(211, 47, 47, 0.04);
  --palette-error_5: rgba(211, 47, 47, 0.05);
  --palette-error_10: rgba(211, 47, 47, 0.1);
  --palette-error_20: rgba(211, 47, 47, 0.2);
  --palette-error_50: rgba(211, 47, 47, 0.5);
  --palette-mint: #37c785;
  --palette-mint_10: rgba(55, 199, 133, 0.1);
  --palette-mint_30: rgba(55, 199, 133, 0.3);
  --palette-navyBlue: #275395;
  --palette-navyBlue_10: rgba(39, 83, 149, 0.1);
  --palette-disabledError: #d5898d;
  --palette-pendingOrange: #ff8c00;
  --palette-orange: #fbb15b;
  --palette-pendingOrange_4: rgba(255, 140, 0, 0.04);
  --palette-pendingOrange_10: rgba(255, 140, 0, 0.1);
  --palette-pendingOrange_20: rgba(255, 140, 0, 0.2);
  --palette-pendingOrange_50: rgba(255, 140, 0, 0.5);
  --palette-muteMidGray: #606060;
  --palette-disabledGrayDark: #848484;
  --palette-disabledGray: #cacaca;
  --palette-techGray_20: rgba(132, 147, 168, 0.2);
  --palette-techGray_10: rgba(132, 147, 168, 0.1);
  --palette-hover_2: #e6e9ee;
  --palette-hover: #f1f4f9;
  --palette-hoverGray_15: rgba(132, 147, 168, 0.15);
  --palette-navHover_10: rgba(132, 147, 168, 0.1);
  --palette-hover_6: rgba(132, 147, 168, 0.6);
  --palette-hover_50: rgba(241, 244, 249, 0.5);
  --palette-iceBlue: #eef5ff;
  --palette-transparent: transparent;
  --palette-brown: #8a6351;
  --palette-brown_4: rgba(138, 99, 81, 0.04);
  --palette-darkBackground: #171717;
  --palette-darkHeaderBg: #2f2f2f;
  --palette-headerBg: #303030;
  --palette-popOverBg: #242424;
  --palette-lightboxBackdrop: #0f0f0f;
  --palette-popOverBg_50: rgb(36, 36, 36, 0.5);
  --palette-popOverBg_60: rgba(36, 36, 36, 0.6);
  --palette-popOverBg_90: rgba(36, 36, 36, 0.9);
  --palette-hover_75: #353535;
  --palette-gold: #d9b97d;
  --palette-background2Light: #f3f5f7;
  --palette-background2Darker: #2d2f31;
  --palette-background2Light_60: rgba(243, 245, 247, 0.6);
  --palette-background2Dark: #181818;
  --palette-background3Dark: #252525;
  --palette-darkHover: #1b1b1b;
  --palette-darkHover_2: #484848;
  --palette-black: #000000;
  --palette-black_05: rgba(0, 0, 0, 0.05);
  --palette-black_10: rgba(0, 0, 0, 0.1);
  --palette-black_16: rgba(0, 0, 0, 0.16);
  --palette-black_20: rgba(0, 0, 0, 0.2);
  --palette-black_30: rgba(0, 0, 0, 0.3);
  --palette-black_40: rgba(0, 0, 0, 0.4);
  --palette-black_50: rgba(0, 0, 0, 0.5);
  --palette-black_80: rgba(0, 0, 0, 0.8);
  --palette-darkSecondary: #393939;
  --palette-darkSecondary_10: rgb(57, 57, 57, 0.1);
  --palette-darkSecondary_20: rgb(57, 57, 57, 0.2);
  --palette-lightGreen: #95c039;
  --palette-lightestGreen: #8ac926;
  --palette-lightError: rgba(211, 47, 47, 0.05);
  --palette-borderError: rgba(211, 47, 47, 0.3);
  --palette-lightGray: #f7f7f8;
  --palette-darkModalHeaderBackground: linear-gradient(
    180deg,
    #363636 0%,
    #202020 100%
  );
  --palette-lightModalHeaderBackground: linear-gradient(
    191.69deg,
    #eef5ff -0.77%,
    #ffffff 61.92%
  );
  --palette-barGradiantLight: linear-gradient(90deg, #83b4ff 0%, #5384ee 100%);
  --palette-barGradiantDark: linear-gradient(0deg, #4a4a4a 0%, #313131 100%);
  --palette-tableBgDarkOdd: #272727;
  --palette-tableBgDarkEven: #292929;
  --palette-cornflowerBlue: #1982c4;
  --palette-cornflowerBlueDark: #14689c;
  --palette-heliotrope: #9d57fc;
  --palette-heliotrope_10: #351c56;
  --palette-heliotropeDark: #7d45c9;
  --palette-darkTangerine: #ffbd00;
  --palette-darkTangerineDark: #cc9700;
  --palette-darkError: #ff595e;
  --palette-darkErrorDark: #cc474b;
  --palette-lightBrown: #a86464;
  --palette-lightBrownDark: #865050;
  --palette-blueGradientLayover: linear-gradient(
    180deg,
    rgba(83, 132, 238, 0) 0%,
    #5384ee 100%
  );
  --palette-blueGradientLayoverHover: linear-gradient(
    180deg,
    rgba(83, 132, 238, 0.4) 0%,
    #5384ee 100%
  );
  --palette-modalBlur: rgba(0, 0, 0, 0.5);
  --palette-like-bg: #eef3fe;
  --palette-dark-like-bg: #282d38;
  --palette-boost-bg: #fff4e6;
  --palette-dark-boost-bg: #392e20;
  --palette-darker-boost-bg: #392e22;
  --palette-celeb-bg: #edf6ed;
  --palette-dark-celeb-bg: #273027;
  --palette-ban-bg: #fdf4f4;
  --palette-dark-ban-bg: #2d2424;
  --palette-popOverBg_50: rgba(36, 36, 36, 0.1);
  --palette-gold: #eebd53;
  --palette-goldHover: #be9742;
  --palette-brownish: #8e7131;
  --palette-semi_brand: #c2d7fb;
  --palette-lightCookiePopup: linear-gradient(
    111deg,
    rgba(255, 255, 255, 0.5) 0.41%,
    rgba(255, 255, 255, 0.7) 98.56%
  );
  --palette-darkCookiePopup: linear-gradient(
    111deg,
    rgba(36, 36, 36, 0.5) 0.41%,
    rgba(36, 36, 36, 0.7) 98.56%
  );

  /* Tokens */
  --palette-editor_50: #fcf7f0;
  --palette-editor_100: #f8ebdc;
  --palette-editor_200: #efd4b9;
  --palette-editor_300: #e5b68c;
  --palette-editor_400: #da915d;
  --palette-editor_400_4: rgba(218, 145, 93, 0.04);
  --palette-editor_400_10: rgba(218, 145, 93, 0.1);
  --palette-editor_400_20: rgba(218, 145, 93, 0.2);
  --palette-editor_500: #d1753e;
  --palette-editor_600: #c35f33;
  --palette-editor_600_4: rgba(195, 95, 351, 0.04);
  --palette-editor_600_10: rgba(195, 95, 351, 0.1);
  --palette-editor_600_20: rgba(195, 95, 351, 0.2);
  --palette-editor_700: #9b472a;
  --palette-editor_800: #823d2a;
  --palette-editor_900: #693425;
  --palette-editor_950: #381912;

  --palette-recruiter_50: #f5f7fa;
  --palette-recruiter_100: #eaeff4;
  --palette-recruiter_200: #d1dce6;
  --palette-recruiter_300: #a9bdd0;
  --palette-recruiter_400: #7b9ab5;
  --palette-recruiter_400_4: rgba(123, 154, 181, 0.04);
  --palette-recruiter_400_10: rgba(123, 154, 181, 0.1);
  --palette-recruiter_400_20: rgba(123, 154, 181, 0.2);
  --palette-recruiter_500: #5a7e9d;
  --palette-recruiter_600: #476582;
  --palette-recruiter_700: #3a526a;
  --palette-recruiter_800: #334659;
  --palette-recruiter_900: #2e3c4c;
  --palette-recruiter_950: #1f2833;

  --palette-service_50: #f1f7ee;
  --palette-service_100: #e1eed9;
  --palette-service_200: #c6deb8;
  --palette-service_300: #a2c88e;
  --palette-service_400: #80b269;
  --palette-service_500: #63964c;
  --palette-service_500_4: rgba(99, 150, 76, 0.04);
  --palette-service_500_10: rgba(99, 150, 76, 0.1);
  --palette-service_500_20: rgba(99, 150, 76, 0.2);
  --palette-service_600: #4c783a;
  --palette-service_600_4: rgba(76, 120, 58, 0.04);
  --palette-service_600_10: rgba(76, 120, 58, 0.1);
  --palette-service_600_20: rgba(76, 120, 58, 0.2);
  --palette-service_700: #3c5c2f;
  --palette-service_800: #334a2a;
  --palette-service_900: #2e4027;
  --palette-service_950: #152211;

  --palette-campaign_50: #fff1f1;
  --palette-campaign_100: #fee5e6;
  --palette-campaign_200: #fdced2;
  --palette-campaign_300: #fba6ad;
  --palette-campaign_400: #f77581;
  --palette-campaign_500: #ee384e;
  --palette-campaign_500_4: rgba(238, 56, 78, 0.04);
  --palette-campaign_500_10: rgba(238, 56, 78, 0.1);
  --palette-campaign_500_20: rgba(238, 56, 78, 0.2);
  --palette-campaign_600: #dc2242;
  --palette-campaign_700: #b91736;
  --palette-campaign_800: #9b1634;
  --palette-campaign_900: #851632;
  --palette-campaign_950: #4a0717;

  --palette-sales_50: #fef8ec;
  --palette-sales_100: #fce9c9;
  --palette-sales_200: #f8d18f;
  --palette-sales_300: #f5b454;
  --palette-sales_400: #f2992c;
  --palette-sales_500: #eb7815;
  --palette-sales_500_4: rgba(242, 153, 44, 0.04);
  --palette-sales_500_10: rgba(242, 153, 44, 0.1);
  --palette-sales_500_20: rgba(242, 153, 44, 0.2);
  --palette-sales_600: #d0550f;
  --palette-sales_700: #ad3910;
  --palette-sales_800: #8d2c13;
  --palette-sales_900: #742513;
  --palette-sales_950: #421106;

  /* Tokens */

  /* palette  */ /* colors */
  --sky: var(--palette-sky);
  --coal: var(--palette-coal);
  --cloud: var(--palette-cloud);
  --black: var(--palette-black);
  --black_10: var(--palette-black_10);
  --black_16: var(--palette-black_16);
  --black_20: var(--palette-black_20);
  --black_30: var(--palette-black_30);
  --black_50: var(--palette-black_50);
  --black_40: var(--palette-black_40);
  --black_80: var(--palette-black_80);
  --brand: var(--palette-brand);
  --darkBrand: var(--palette-darkBrand);
  --darkBrandHover: var(--palette-darkBrandHover);
  --brand_10: var(--palette-brand_10);
  --busbrand_10: var(--palette-busbrand_10);
  --brand_20: var(--palette-brand_20);
  --brand_30: var(--palette-brand_30);
  --brand_70: var(--palette-brand_70);
  --barBgLight: var(--palette-barBgLight);
  --blueFog: var(--palette-blueFog);
  --arcticBlue: var(--palette-arcticBlue);
  --darkSecondary: var(--palette-darkSecondary);
  --popOverBg: var(--palette-popOverBg);
  --gray: var(--palette-gray);
  --gray_5: var(--palette-gray_5);
  --gray_10: var(--palette-gray_10);
  --gray_30: var(--palette-gray_30);
  --gray_20: var(--palette-gray_20);
  --gray_50: var(--palette-gray_50);
  --gray_60: var(--palette-gray_60);
  --gray_70: var(--palette-gray_70);
  --hover: var(--palette-hover);
  --hover_2: var(--palette-hover_2);
  --hover_50: var(--palette-hover_50);
  --hoverGray_15: var(--palette-hoverGray_15);
  --iceBlue: var(--palette-iceBlue);
  --transparent: var(--palette-transparent);
  --error: var(--palette-error);
  --error_4: var(--palette-error_4);
  --error_5: var(--palette-error_5);
  --error_10: var(--palette-error_10);
  --error_20: var(--palette-error_20);
  --error_50: var(--palette-error_50);
  --disabledError: var(--palette-disabledError);
  --pendingOrange: var(--palette-pendingOrange);
  --orange: var(--palette-orange);
  --pendingOrange_4: var(--palette-pendingOrange_4);
  --pendingOrange_10: var(--palette-pendingOrange_10);
  --pendingOrange_50: var(--palette-pendingOrange_50);
  --muteMidGray: var(--palette-muteMidGray);
  --white: var(--palette-white);
  --white_10: var(--palette-white_10);
  --white_20: var(--palette-white_20);
  --white_40: var(--palette-white_40);
  --white_50: var(--palette-white_50);
  --white_80: var(--palette-white_80);
  --background2Light: var(--palette-background2Light);
  --mint: var(--palette-mint);
  --mint_10: var(--palette-mint_10);
  --mint_30: var(--palette-mint_30);
  --navyBlue: var(--palette-navyBlue);
  --navyBlue_10: var(--palette-navyBlue_10);
  --navHover_10: var(--palette-navHover_10);
  --graphene: var(--palette-graphene);
  --graphene_60: var(--palette-graphene_60);
  --techGray_20: var(--palette-techGray_20);
  --techGray_10: var(--palette-techGray_10);
  --hover_75: var(--palette-hover_75);
  --disabledGray: var(--palette-disabledGray);
  --disabledGrayDark: var(--palette-disabledGrayDark);
  --highlightIcon: var(--palette-highlightIcon);
  --warning: var(--palette-warning);
  --irrelevant: var(--palette-irrelevant);
  --irrelevant_10: var(--palette-irrelevant_10);
  --irrelevant_30: var(--palette-irrelevant_30);
  --success: var(--palette-success);
  --success_5: var(--palette-success_5);
  --success_4: var(--palette-success_4);
  --success_10: var(--palette-success_10);
  --snow: var(--palette-snow);
  --snow_90: var(--palette-snow_90);
  --trench: var(--palette-trench);
  --warning_5: var(--palette-warning_5);
  --warning_10: var(--palette-warning_10);
  --warning_30: var(--palette-warning_30);
  --brand_5: var(--palette-brand_5);
  --brand_4: var(--palette-brand_4);
  --background2Dark: var(--palette-background2Dark);
  --lightGray: var(--palette-lightGray);
  --brown: var(--palette-brown);
  --brown_4: var(--palette-brown_4);
  --lightboxBackdrop: var(--palette-lightboxBackdrop);
  --lightGreen: var(--palette-lightGreen);
  --lightError: var(--palette-lightError);
  --borderError: var(--palette-borderError);
  --darkHover: var(--palette-darkHover);
  --darkHover_2: var(--palette-darkHover_2);
  --lead: var(--palette-lead);
  --lightBrownDark: var(--palette-lightBrownDark);
  --cornflowerBlue: var(--palette-cornflowerBlue);
  --cornflowerBlueDark: var(--palette-cornflowerBlueDark);
  --heliotrope: var(--palette-heliotrope);
  --heliotrope_10: var(--palette-heliotrope_10);
  --heliotropeDark: var(--palette-heliotropeDark);
  --darkTangerine: var(--palette-darkTangerine);
  --darkTangerineDark: var(--palette-darkTangerineDark);
  --darkError: var(--palette-darkError);
  --darkErrorDark: var(--palette-darkErrorDark);
  --background: var(--palette-white);
  --background2: var(--palette-background2Light);
  --background3: var(--palette-white);
  --background4: var(--palette-white);
  --background6: var(--palette-hover_2);
  --background7: var(--palette-brand);
  --background8: var(--palette-brand_10);
  --background9: var(--palette-snow);
  --background10: var(--palette-brand_10);
  --background11: var(--palette-darkBackground);
  --backgroundIcon: var(--palette-hover);
  --backgroundIconSecondary: var(--palette-hover);
  --colorIcon: var(--palette-black);
  --colorIconSecond: var(--palette-graphene);
  --colorIconThird: var(--palette-smoke);
  --colorIconForth: var(--palette-graphene);
  --colorIconForth2: var(--palette-graphene_60);
  --colorIconFifth: var(--palette-brand);
  --colorIconSixth: var(--palette-brand_30);
  --colorIconSeventh: var(--palette-brand);
  --colorIconEighth: var(--palette-techGray_20);
  --colorIconNinth: var(--palette-brand);
  --colorIconTen: var(--palette-gray_50);
  --border: var(--palette-gray);
  --borderSecond: var(--palette-transparent);
  --borderThird: var(--palette-brand_5);
  --borderForth: var(--palette-snow);
  --borderFifth: var(--palette-smoke);
  --borderSixth: var(--palette-techGray_20);
  --borderSeventh: var(--palette-gray_50);
  --borderEighth: var(--palette-techGray_20);
  --muteMidGray_techGray_20: var(--palette-techGray_20);
  --muteMidGray_coal: var(--palette-coal);
  --smoke_coal: var(--palette-coal);
  --smoke_coal_30: var(--palette-coal_30);
  --primaryText: var(--palette-graphene);
  --secondaryText: var(--palette-coal);
  --brand5_gray10: var(--palette-brand_5);
  --thirdText: var(--palette-coal);
  --forthText: var(--palette-highlightIcon);
  --fifthText: var(--palette-graphene);
  --sixthText: var(--palette-brand);
  --tenthText: var(--palette-graphene);
  --brand20_brand: var(--palette-brand);
  --brand_trench: var(--palette-trench);
  --brand_white: var(--palette-white);
  --smoke_brand: var(--palette-brand);
  --disabledGray_muteMidGray: var(--palette-muteMidGray);
  --disabledGray_highlightIcon: var(--palette-highlightIcon);
  --tooltipText: var(--palette-smoke);
  --leadText: var(--palette-lead2);
  --seventhText: var(--palette-highlightIcon);
  --eighthText: var(--palette-trench);
  --ninthText: var(--palette-gray);
  --primaryDisabledText: var(--palette-gray);
  --primaryDisabledTextReverted: var(--palette-muteMidGray);
  --gray_techGray20: var(--palette-techGray_20);
  --secondaryDisabledText: var(--palette-gray);
  --barProgress: var(--palette-barGradiantLight);
  --barBg: var(--palette-barBgLight);
  --smoke: var(--palette-smoke);
  --smoke2: var(--palette-smoke);
  --smoke_trench: var(--palette-trench);
  --muteMidGray_disabledGray: var(--palette-disabledGray);
  --modalHeaderBackground: var(--palette-lightModalHeaderBackground);
  --tableRowBgOdd: var(--palette-hover_50);
  --tableRowBgEven: var(--palette-snow);
  --hoverPrimary: var(--palette-hover_2);
  --hoverPrimary2: var(--palette-hover_2);
  --hover75_hoverGray15: var(--palette-hoverGray_15);
  --hoverThird: var(--palette-hover_2);
  --hoverSecondary: var(--palette-sky_30);
  --skeletonBg: var(--palette-smoke);
  --thumbnailHover: var(--palette-white_80);
  --linkHoverColor: var(--palette-black_80);
  --inputPlaceholder: var(--palette-gray_50);
  --input: var(--palette-coal);
  --tagHover: var(--palette-brand_20);
  --white2040: var(--palette-white_40);
  --darkHeaderBg_gray_5: var(--palette-gray_5);
  --darkHeaderBg_snow: var(--palette-snow);
  --snow_brand70: var(--palette-brand_70);
  --topBarShadow: var(--palette-black_05);
  --hover2_coal: var(--palette-coal);
  --darkHover_hover: var(--palette-hover);
  --darkHover2_hover: var(--palette-hover);
  --hover_75_hover: var(--palette-hover);
  --black80_white80: var(--palette-white_80);
  --black50_white50: var(--palette-white_50);
  --headerBg_brand10: var(--palette-brand_10);
  --darkSecondary_hover_50: var(--palette-hover_50);
  --disabledGrayDark_coal: var(--palette-coal);
  --hover_75_background2Light: var(--palette-background2Light);
  --darkSecondary_autofillLight: var(--palette-autofillLight);
  --trench_sky: var(--palette-sky);
  --background2Light_White: var(--palette-white);
  --background2Light_coal: var(--palette-coal);
  --darkHeaderBg_white: var(--palette-white);
  --darkSecondary_hoverGray15: var(--palette-hoverGray_15);
  --smoke_gray: var(--palette-gray);
  --darkHeaderBg_hoverGray15: var(--palette-hoverGray_15);
  --brand_brand20: var(--palette-brand_20);
  --brand10_brand20: var(--palette-brand_20);
  --white_hover2: var(--palette-hover_2);
  --darkSecondary_brand10: var(--palette-brand_10);
  --darkSecondary_brand20: var(--palette-brand_20);
  --disabledGray_hover: var(--palette-hover);
  --disabledGray_coal: var(--palette-coal);
  --disabledGray_graphene: var(--palette-graphene);
  --trench_brand20: var(--palette-brand_20);
  --hover2_graphene: var(--palette-graphene);
  --white_popOverBg: var(--palette-popOverBg);
  --popOverBg_white: var(--palette-white);
  --smoke_hover75: var(--palette-smoke);
  --coal_disabledGray: var(--palette-disabledGray);
  --graphene_hover: var(--palette-hover);
  --gray_graphene: var(--palette-graphene);
  --hover75_techGray_20: var(--palette-techGray_20);
  --darkSecondary_gray10: var(--palette-gray_10);
  --darkSecondary_gray: var(--palette-gray);
  --darkSecondary_20_gray_10: var(--palette-gray_10);
  --background2Light_60_popOverBg_60: var(--palette-background2Light_60);
  --smoke_lead: var(--palette-lead);
  --disabledGrayDark_disabledGray: var(--palette-disabledGray);
  --darkSecondary10_white10: var(--palette-white_10);
  --gray10_hover2: var(--palette-hover_2);
  --muteMidGray_gray80: var(--palette-gray_80);
  --lightBrown: var(--palette-lightBrown);
  --muteMidGray_hover_6: var(--palette-hover_6);
  --blueGradientLayover: var(--palette-blueGradientLayover);
  --blueGradientLayoverHover: var(--palette-blueGradientLayoverHover);
  --modalBlur: var(--palette-modalBlur);
  --popoverBg_hover: var(--palette-hover);
  --white_04_black_05: var(--palette-black_05);
  --like-bg: var(--palette-like-bg);
  --boost-bg: var(--palette-boost-bg);
  --celeb-bg: var(--palette-celeb-bg);
  --ban-bg: var(--palette-ban-bg);
  --disabledGrayDark_gray: var(--palette-gray);
  --wash: var(--palette-wash-light);
  --hover75_hover2: var(--palette-hover_2);
  --darkSecondary_hover: var(--palette-hover);
  --pale_background: var(--palette-white_10);
  --white_60: var(--palette-white_60);
  --little_transparent_background: var(--palette-white_90);
  --ban_bg: var(--palette-ban-bg);
  --background2_dark_light: var(--palette-background2Light);
  --celeb_bg_dark_light: var(--palette-celeb-bg);
  --booster: var(--palette-boost-bg);
  --darkBackground_background2light: var(--palette-background2Light);
  --graphene60_gray60: var(--palette-gray_60);
  --gold: var(--palette-gold);
  --goldHover: var(--palette-goldHover);
  --brownish: var(--palette-brownish);
  --success_20: var(--palette-success_20);
  --pendingOrange_20: var(--palette-pendingOrange_20);
  --semi_brand: var(--palette-semi_brand);
  --cookiePopupBG: var(--palette-lightCookiePopup);

  --module_brand: var(--palette-brand);
  --module_brand_4: var(--palette-brand_4);
  --module_brand_10: var(--palette-brand_10);
  --module_brand_20: var(--palette-brand_20);
  --module_semi_brand: var(--palette-semi_brand);
  --module_darkBrand: var(--palette-darkBrand);
  --module_darkBrandHover: var(--palette-darkBrandHover);
  --module_trench: var(--palette-trench);

  /* breakpoints */
  --smallMobileBreakpoint: 320px;
  --tabletBreakpoint: 1124px;
  --midDesktopBreakpoint: 1312px;
  --desktopBreakpoint: 1440;

  /* breakpoints */
  /* custom */
  --l-safe-area-bottom: 0px;
  --palette-glassEffect_50: rgb(36, 36, 36, 0.5);
  --palette-glassEffect_70: rgb(36, 36, 36, 0.7);
}

.module-editor-light {
  --module_brand: var(--palette-editor_600);
  --module_brand_4: var(--palette-editor_600_4);
  --module_brand_10: var(--palette-editor_600_10);
  --module_brand_20: var(--palette-editor_600_20);
  --module_semi_brand: var(--palette-editor_300);
  --module_darkBrand: var(--palette-editor_800);
  --module_darkBrandHover: var(--palette-editor_700);
  --module_trench: var(--palette-editor_500);
}
.module-recruiter-light {
  --module_brand: var(--palette-recruiter_700);
  --module_brand_4: var(--palette-recruiter_400_4);
  --module_brand_10: var(--palette-recruiter_400_10);
  --module_brand_20: var(--palette-recruiter_400_20);
  --module_semi_brand: var(--palette-recruiter_300);
  --module_darkBrand: var(--palette-recruiter_900);
  --module_darkBrandHover: var(--palette-recruiter_800);
  --module_trench: var(--palette-recruiter_500);
}
.module-service-light {
  --module_brand: var(--palette-service_600);
  --module_brand_4: var(--palette-service_600_4);
  --module_brand_10: var(--palette-service_600_10);
  --module_brand_20: var(--palette-service_600_20);
  --module_semi_brand: var(--palette-service_300);
  --module_darkBrand: var(--palette-service_800);
  --module_darkBrandHover: var(--palette-service_700);
  --module_trench: var(--palette-service_500);
}
.module-campaign-light {
  --module_brand: var(--palette-campaign_500);
  --module_brand_4: var(--palette-campaign_500_4);
  --module_brand_10: var(--palette-campaign_500_10);
  --module_brand_20: var(--palette-campaign_500_20);
  --module_semi_brand: var(--palette-campaign_300);
  --module_darkBrand: var(--palette-campaign_700);
  --module_darkBrandHover: var(--palette-campaign_600);
  --module_trench: var(--palette-campaign_400);
}
.module-sales-light {
  --module_brand: var(--palette-sales_500);
  --module_brand_4: var(--palette-sales_500_4);
  --module_brand_10: var(--palette-sales_500_10);
  --module_brand_20: var(--palette-sales_500_20);
  --module_semi_brand: var(--palette-sales_300);
  --module_darkBrand: var(--palette-sales_700);
  --module_darkBrandHover: var(--palette-sales_600);
  --module_trench: var(--palette-sales_400);
}

.dark-theme {
  --background: var(--palette-popOverBg);
  --background2: var(--palette-background2Dark);
  --background3: var(--palette-background3Dark);
  --background4: var(--palette-darkSecondary);
  --background6: var(--palette-darkSecondary);
  --background7: var(--palette-disabledGray);
  --background8: var(--palette-darkSecondary);
  --background9: var(--palette-popOverBg);
  --background10: var(--palette-hover_75);
  --background11: var(--palette-smoke);
  --backgroundIcon: var(--palette-hover_75);
  --backgroundIconSecondary: var(--palette-darkSecondary);
  --colorIcon: var(--palette-disabledGray);
  --colorIconSecond: var(--palette-white);
  --colorIconThird: var(--palette-disabledGrayDark);
  --colorIconForth: var(--palette-muteMidGray);
  --colorIconForth2: var(--palette-muteMidGray);
  --colorIconFifth: var(--palette-muteMidGray);
  --colorIconSixth: var(--palette-hover_75);
  --colorIconSeventh: var(--palette-disabledGray);
  --colorIconEighth: var(--palette-disabledGrayDark);
  --colorIconNinth: var(--palette-disabledGrayDark);
  --colorIconTen: var(--palette-disabledGray);
  --border: var(--palette-disabledGrayDark);
  --borderSecond: var(--palette-techGray_20);
  --borderThird: var(--palette-techGray_20);
  --borderForth: var(--palette-darkSecondary_10);
  --borderFifth: var(--palette-muteMidGray);
  --borderSixth: var(--palette-darkSecondary);
  --borderSeventh: var(--palette-muteMidGray);
  --borderEighth: var(--palette-muteMidGray);
  --muteMidGray_techGray_20: var(--palette-muteMidGray);
  --muteMidGray_coal: var(--palette-muteMidGray);
  --smoke_coal: var(--palette-smoke);
  --smoke_coal_30: var(--palette-smoke_30);
  --primaryText: var(--palette-disabledGray);
  --secondaryText: var(--palette-white);
  --brand5_gray10: var(--palette-gray_10);
  --thirdText: var(--palette-smoke);
  --forthText: var(--palette-disabledGray);
  --fifthText: var(--palette-disabledGrayDark);
  --sixthText: var(--palette-white);
  --tenthText: var(--palette-disabledGray);
  --brand20_brand: var(--palette-brand_20);
  --brand_trench: var(--palette-brand);
  --brand_white: var(--palette-brand);
  --smoke_brand: var(--palette-smoke);
  --disabledGray_muteMidGray: var(--palette-disabledGray);
  --disabledGray_highlightIcon: var(--palette-disabledGrayDark);
  --tooltipText: var(--palette-darkBackground);
  --leadText: var(--palette-disabledGray);
  --seventhText: var(--palette-muteMidGray);
  --eighthText: var(--palette-muteMidGray);
  --ninthText: var(--palette-disabledGray);
  --primaryDisabledText: var(--palette-muteMidGray);
  --primaryDisabledTextReverted: var(--palette-gray);
  --gray_techGray20: var(--palette-gray);
  --secondaryDisabledText: var(--palette-disabledGrayDark);
  --barProgress: var(--palette-barGradiantDark);
  --barBg: var(--palette-muteMidGray);
  --smoke2: var(--palette-muteMidGray);
  --smoke_trench: var(--palette-smoke);
  --muteMidGray_disabledGray: var(--palette-muteMidGray);
  --modalHeaderBackground: var(--palette-popOverBg);
  --tableRowBgOdd: var(--palette-tableBgDarkOdd);
  --tableRowBgEven: var(--palette-tableBgDarkEven);
  --hoverPrimary: var(--palette-hover_75);
  --hoverPrimary2: var(--palette-hover_6);
  --hover75_hoverGray15: var(--palette-hover_75);
  --hoverThird: var(--palette-muteMidGray);
  --hoverSecondary: var(--palette-popOverBg_50);
  --skeletonBg: var(--palette-darkSecondary);
  --thumbnailHover: var(--palette-black_80);
  --linkHoverColor: var(--palette-white_80);
  --inputPlaceholder: var(--palette-disabledGrayDark);
  --input: var(--palette-smoke);
  --tagHover: var(--palette-muteMidGray);
  --white2040: var(--palette-white_20);
  --darkHeaderBg_gray_5: var(--palette-darkHeaderBg);
  --darkHeaderBg_snow: var(--palette-darkHeaderBg);
  --snow_brand70: var(--palette-snow);
  --topBarShadow: var(--palette-black_20);
  --hover2_coal: var(--palette-hover_2);
  --darkHover_hover: var(--palette-darkHover);
  --darkHover2_hover: var(--palette-darkHover_2);
  --hover_75_hover: var(--palette-hover_75);
  --black80_white80: var(--palette-black_80);
  --black50_white50: var(--palette-black_50);
  --headerBg_brand10: var(--palette-headerBg);
  --darkSecondary_hover_50: var(--palette-darkSecondary);
  --disabledGrayDark_coal: var(--palette-disabledGrayDark);
  --hover_75_background2Light: var(--palette-hover_75);
  --darkSecondary_autofillLight: var(--palette-darkSecondary);
  --trench_sky: var(--palette-trench);
  --background2Light_White: var(--palette-background2Light);
  --background2Light_coal: var(--palette-background2Light);
  --darkHeaderBg_white: var(--palette-darkHeaderBg);
  --darkSecondary_hoverGray15: var(--palette-darkSecondary);
  --smoke_gray: var(--palette-smoke);
  --darkHeaderBg_hoverGray15: var(--palette-darkHeaderBg);
  --brand_brand20: var(--palette-brand);
  --brand10_brand20: var(--palette-brand_10);
  --white_hover2: var(--palette-white);
  --darkSecondary_brand10: var(--palette-darkSecondary);
  --darkSecondary_brand20: var(--palette-darkSecondary);
  --disabledGray_hover: var(--palette-disabledGray);
  --disabledGray_coal: var(--palette-disabledGray);
  --disabledGray_graphene: var(--palette-disabledGray);
  --trench_brand20: var(--palette-trench);
  --hover2_graphene: var(--palette-hover_2);
  --white_popOverBg: var(--palette-white);
  --popOverBg_white: var(--palette-popOverBg);
  --smoke_hover75: var(--palette-hover_75);
  --coal_disabledGray: var(--palette-coal);
  --graphene_hover: var(--palette-graphene);
  --gray_graphene: var(--palette-gray);
  --hover75_techGray_20: var(--palette-hover_75);
  --darkSecondary_gray10: var(--palette-darkSecondary);
  --darkSecondary_gray: var(--palette-darkSecondary);
  --darkSecondary_20_gray_10: var(--palette-darkSecondary_20);
  --background2Light_60_popOverBg_60: var(--palette-popOverBg_60);
  --smoke_lead: var(--palette-smoke);
  --disabledGrayDark_disabledGray: var(--palette-disabledGrayDark);
  --darkSecondary10_white10: var(--palette-darkSecondary_10);
  --gray10_hover2: var(--palette-gray_10);
  --muteMidGray_gray80: var(--palette-muteMidGray);
  --lightbrown: --palette-lightBrown;
  --muteMidGray_hover_6: var(--palette-muteMidGray);
  --popoverBg_hover: var(--palette-popOverBg);
  --white_04_black_05: var(--palette-white_04);
  --like-bg: var(--palette-dark-like-bg);
  --boost-bg: var(--palette-dark-boost-bg);
  --celeb-bg: var(--palette-dark-celeb-bg);
  --ban-bg: var(--palette-dark-ban-bg);
  --disabledGrayDark_gray: var(--palette-disabledGrayDark);
  --wash: var(--palette-wash-dark);
  --hover75_hover2: var(--palette-hover_75);
  --darkSecondary_hover: var(--palette-darkSecondary);
  --pale_background: var(--palette-popOverBg_50);
  --white_60: var(--palette-white_60);
  --little_transparent_background: var(--palette-popOverBg_90);
  --ban_bg: var(--palette-dark-ban-bg);
  --background2_dark_light: var(--palette-background2Darker);
  --celeb_bg_dark_light: var(--palette-dark-celeb-bg);
  --booster: var(--palette-darker-boost-bg);
  --lightestGreen: var(--palette-lightestGreen);
  --darkBackground_background2light: var(--palette-darkBackground);
  --graphene60_gray60: var(--palette-graphene_60);
  --cookiePopupBG: var(--palette-darkCookiePopup);
}

.module-editor-dark {
  --module_brand: var(--palette-editor_600);
  --module_brand_4: var(--palette-editor_400_4);
  --module_brand_10: var(--palette-editor_400_10);
  --module_brand_20: var(--palette-editor_400_20);
  --module_semi_brand: var(--palette-editor_300);
  --module_darkBrand: var(--palette-editor_800);
  --module_darkBrandHover: var(--palette-editor_700);
  --module_trench: var(--palette-editor_500);
}
.module-recruiter-dark {
  --module_brand: var(--palette-recruiter_400);
  --module_brand_4: var(--palette-recruiter_400_4);
  --module_brand_10: var(--palette-recruiter_400_10);
  --module_brand_20: var(--palette-recruiter_400_20);
  --module_semi_brand: var(--palette-recruiter_300);
  --module_darkBrand: var(--palette-recruiter_600);
  --module_darkBrandHover: var(--palette-recruiter_500);
  --module_trench: var(--palette-recruiter_300);
}
.module-service-dark {
  --module_brand: var(--palette-service_500);
  --module_brand_4: var(--palette-service_500_4);
  --module_brand_10: var(--palette-service_500_10);
  --module_brand_20: var(--palette-service_500_20);
  --module_semi_brand: var(--palette-service_300);
  --module_darkBrand: var(--palette-service_700);
  --module_darkBrandHover: var(--palette-service_600);
  --module_trench: var(--palette-service_400);
}
.module-campaign-dark {
  --module_brand: var(--palette-campaign_500);
  --module_brand_4: var(--palette-campaign_500_4);
  --module_brand_10: var(--palette-campaign_500_10);
  --module_brand_20: var(--palette-campaign_500_20);
  --module_semi_brand: var(--palette-campaign_300);
  --module_darkBrand: var(--palette-campaign_700);
  --module_darkBrandHover: var(--palette-campaign_600);
  --module_trench: var(--palette-campaign_400);
}
.module-sales-dark {
  --module_brand: var(--palette-sales_400);
  --module_brand_4: var(--palette-sales_500_4);
  --module_brand_10: var(--palette-sales_500_10);
  --module_brand_20: var(--palette-sales_500_20);
  --module_semi_brand: var(--palette-sales_300);
  --module_darkBrand: var(--palette-sales_500);
  --module_darkBrandHover: var(--palette-sales_500);
  --module_trench: var(--palette-sales_300);
}
