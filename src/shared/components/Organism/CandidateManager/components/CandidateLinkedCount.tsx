import IconButton from '@shared/uikit/Button/IconButton';
import Flex from '@shared/uikit/Flex';
import Icon from '@shared/uikit/Icon';
import Typography from '@shared/uikit/Typography';
import { countUserParticipation } from '@shared/utils/api/jobs';
import { QueryKeys } from '@shared/utils/constants';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import useTranslation from '@shared/utils/hooks/useTranslation';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import { useSearchParams } from 'next/navigation';
import React from 'react';

function CandidateLinkedCount() {
  const { t } = useTranslation();
  const searchParams = useSearchParams();
  const currentEntityId = searchParams.get('currentEntityId');

  const { data, isLoading } = useReactQuery({
    action: {
      apiFunc: () => countUserParticipation({ id: currentEntityId ?? '' }),
      key: [QueryKeys.getPopularPeopleCategories],
    },
    config: {
      enabled: !!currentEntityId,
    },
  });

  console.log('data', data);

  return (
    <Flex
      flexDir="row"
      className="justify-between !p-8 rounded-lg bg-messagesSuccess_10"
    >
      <Flex flexDir="row" className="gap-8" alignItems="center">
        <Icon
          name="user-applicants"
          color="messageSuccess"
          size={20}
          type="far"
        />
        <Typography size={14} font="500" color="smoke_coal">
          {translateReplacer(t('candidate_linked_to_your_jobs'), '1')}
        </Typography>
      </Flex>

      <Flex flexDir="row" className="gap-8" alignItems="center">
        <Typography size={15} font="700" color="brand">
          {t('view_jobs')}
        </Typography>
        <Icon name="times" size={12} color="smoke_coal" type="far" />
      </Flex>
    </Flex>
  );
}

export default CandidateLinkedCount;
