import React, { useState } from 'react';
import { InitialValuesDisplay, displayInitialValues, useInitialValuesLogger } from './InitialValuesDisplay';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import Typography from '@shared/uikit/Typography';

// Example component showing how to use InitialValuesDisplay
export const InitialValuesExample: React.FC = () => {
  const [initialValues, setInitialValues] = useState({
    items: [
      {
        id: '1',
        title: 'Frontend Developer',
        projects: [
          { title: 'E-commerce Platform' },
          { title: 'Mobile App' }
        ]
      },
      {
        id: '2',
        title: 'Backend Developer',
        projects: [
          { title: 'API Gateway' }
        ]
      }
    ]
  });

  // Use the logger hook
  useInitialValuesLogger(initialValues);

  const addJob = () => {
    const newJob = {
      id: Date.now().toString(),
      title: `New Job ${initialValues.items.length + 1}`,
      projects: [
        { title: `Project ${initialValues.items.length + 1}` }
      ]
    };

    setInitialValues(prev => ({
      items: [...prev.items, newJob]
    }));
  };

  const removeJob = () => {
    setInitialValues(prev => ({
      items: prev.items.slice(0, -1)
    }));
  };

  const clearJobs = () => {
    setInitialValues({ items: [] });
  };

  const logInitialValues = () => {
    displayInitialValues(initialValues);
  };

  return (
    <Flex direction="column" gap={16} style={{ padding: '20px', maxWidth: '600px' }}>
      <Typography size={20} font="600" color="primaryText">
        Initial Values Display Example
      </Typography>

      <Flex gap={8}>
        <Button size="sm" onClick={addJob}>
          Add Job
        </Button>
        <Button size="sm" colorSchema="secondary" onClick={removeJob}>
          Remove Job
        </Button>
        <Button size="sm" colorSchema="danger" onClick={clearJobs}>
          Clear All
        </Button>
        <Button size="sm" colorSchema="tertiary" onClick={logInitialValues}>
          Log to Console
        </Button>
      </Flex>

      <InitialValuesDisplay 
        initialValues={initialValues}
        title="Example Initial Values"
      />

      <Typography size={14} font="400" color="secondaryText">
        • Click buttons above to modify the initial values
        • Check browser console for logged output
        • The display updates automatically when values change
      </Typography>
    </Flex>
  );
};

export default InitialValuesExample;
