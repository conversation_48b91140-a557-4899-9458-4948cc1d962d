import type { JobAPIProps } from '@shared/types/jobsProps';
import { getJobsForLinking } from '@shared/utils/api/jobs';
import { QueryKeys } from '@shared/utils/constants';
import CheckboxAction from '../../components/MultiSelectAction';
import type { GetModalPropsFn } from '../base';
import { getAsyncSinglePickerModalProps } from '../base';
import {
  MultiSelectSelectJobItemComponent,
  SingleSelectJobItemComponent,
} from './components';
import MultiSelectFooter from '../../components/MultiSelectFooter';

export const getAsyncSingleJobPickerModalProps: GetModalPropsFn<JobAPIProps> = (
  t
) => ({
  ...getAsyncSinglePickerModalProps(t),
  local: true,
  queryApiFunc: getJobsForLinking,
  queryFuncSpreadParams: false,
  queryKeys: [QueryKeys.getJobsList],
  ItemComponent: SingleSelectJobItemComponent,
  headerProps: {
    title: t('jobs'),
  },
});

export const getAsyncMultiJobPickerModalProps: GetModalPropsFn<JobAPIProps> = (
  t
) => ({
  ...getAsyncSinglePickerModalProps(t),
  local: true,

  queryApiFunc: getJobsForLinking,
  queryFuncSpreadParams: false,
  queryKeys: [QueryKeys.getJobsList],
  ItemComponent: MultiSelectSelectJobItemComponent,
  ActionComponent: CheckboxAction,
  FooterComponent: MultiSelectFooter,
  headerProps: {
    title: t('jobs'),
  },
});
