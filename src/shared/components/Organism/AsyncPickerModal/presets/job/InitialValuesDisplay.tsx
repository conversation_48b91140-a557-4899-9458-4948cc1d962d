import React from 'react';
import Typography from '@shared/uikit/Typography';
import Flex from '@shared/uikit/Flex';

interface InitialValuesDisplayProps {
  initialValues: { items: any[] };
  title?: string;
}

// Component to show initialValues in UI
export const InitialValuesDisplay: React.FC<InitialValuesDisplayProps> = ({ 
  initialValues, 
  title = "Initial Values" 
}) => {
  return (
    <Flex
      direction="column"
      gap={8}
      style={{ 
        padding: '12px', 
        border: '1px solid #e0e0e0', 
        borderRadius: '8px',
        margin: '10px 0',
        backgroundColor: '#f9f9f9'
      }}
    >
      <Typography size={16} font="600" color="primaryText">
        {title}
      </Typography>
      
      {initialValues.items.length > 0 ? (
        <Flex direction="column" gap={4}>
          <Typography size={14} font="500" color="secondaryText">
            Selected Jobs ({initialValues.items.length}):
          </Typography>
          {initialValues.items.map((job, index) => (
            <Flex key={job.id} gap={8} style={{ padding: '8px', backgroundColor: 'white', borderRadius: '4px' }}>
              <Typography size={12} font="400" color="tertiaryText">
                {index + 1}.
              </Typography>
              <Flex direction="column" gap={2}>
                <Typography size={14} font="500" color="primaryText">
                  {job.title}
                </Typography>
                {job.projects && job.projects.length > 0 && (
                  <Typography size={12} font="400" color="secondaryText">
                    Projects: {job.projects.map((p: any) => p.title).join(', ')}
                  </Typography>
                )}
                <Typography size={12} font="400" color="tertiaryText">
                  ID: {job.id}
                </Typography>
              </Flex>
            </Flex>
          ))}
        </Flex>
      ) : (
        <Typography size={14} font="400" color="secondaryText">
          No items selected
        </Typography>
      )}
      
      {/* Raw JSON for debugging */}
      <details style={{ marginTop: '8px' }}>
        <summary style={{ cursor: 'pointer', fontSize: '12px', color: '#666' }}>
          Show Raw JSON
        </summary>
        <pre style={{ 
          fontSize: '11px', 
          backgroundColor: '#f0f0f0', 
          padding: '8px', 
          borderRadius: '4px',
          overflow: 'auto',
          maxHeight: '200px'
        }}>
          {JSON.stringify(initialValues, null, 2)}
        </pre>
      </details>
    </Flex>
  );
};

// Utility function to log and return initialValues
export const displayInitialValues = (initialValues: { items: any[] }) => {
  console.log('Initial Values:', initialValues);
  console.table(initialValues.items);
  return initialValues;
};

// Hook to use initialValues with logging
export const useInitialValuesLogger = (initialValues: { items: any[] }) => {
  React.useEffect(() => {
    console.group('Initial Values Updated');
    console.log('Items count:', initialValues.items.length);
    console.log('Items:', initialValues.items);
    console.groupEnd();
  }, [initialValues]);

  return initialValues;
};

export default InitialValuesDisplay;
