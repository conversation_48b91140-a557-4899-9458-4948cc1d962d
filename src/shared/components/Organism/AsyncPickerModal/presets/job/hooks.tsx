import { useMemo } from 'react';
import type { CandidateFormData } from '@shared/types/candidates';
import type { JobAPIProps } from '@shared/types/jobsProps';
import AvatarCard from '@shared/uikit/AvatarCard';
import { linkJobsToCandidate } from '@shared/utils/api/candidates';
import { QueryKeys } from '@shared/utils/constants';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { useQueryClient } from '@tanstack/react-query';
import useToast from '@shared/uikit/Toast/useToast';
import Tooltip from '@shared/uikit/Tooltip';
import Icon from '@shared/uikit/Icon';
import Typography from '@shared/uikit/Typography';
import { getCandidacyLinkedJobs } from '@shared/utils/api/jobs';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import { getAsyncMultiJobPickerModalProps, displayInitialValues } from './utils';
import { useAsyncPickerModal } from '../..';
import { InitialValuesDisplay, useInitialValuesLogger } from './InitialValuesDisplay';
import classes from '../base.module.scss';

export const useLinkJobsToCandidate = (candidate?: CandidateFormData) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const toast = useToast();

  const onAlert = async (
    message: string,
    type: 'success' | 'error',
    title = 'candidate'
  ) => {
    toast({
      type,
      icon: `${type === 'success' ? 'check' : 'times'}-circle`,
      title: type === 'error' ? t('error') : t(title),
      message: t(message),
    });
  };

  const linkedJobsQuery = useReactQuery({
    action: {
      apiFunc: () =>
        getCandidacyLinkedJobs(candidate?.id || '').then((items) =>
          items.map((c) => ({ ...c.job, id: String(c.job.id) }))
        ),
      key: [QueryKeys.getCandidateRecruiterJobsList, candidate?.id],
    },
    config: {
      enabled: !!candidate?.id,
    },
  });

  const initialValues = useMemo(
    () => ({
      items: linkedJobsQuery.data ?? [],
    }),
    [linkedJobsQuery.data]
  );

  // Use the logger hook to track initialValues changes
  useInitialValuesLogger(initialValues);

  // Display initialValues for debugging
  displayInitialValues(initialValues);

  return useAsyncPickerModal({
    ...getAsyncMultiJobPickerModalProps(t),
    initialValues,
    // enableReinitialize: true,
    headerProps: {
      title: t('link_jobs'),
      rightContent: () => (
        <Tooltip
          placement="top"
          triggerWrapperClassName={classes.tooltipWrapper}
          trigger={
            <Icon color="primaryText" type="far" name="info-circle" size={20} />
          }
        >
          <Typography size={14} font="400" color="tooltipText">
            {t('candidate_link_job_desc')}
          </Typography>
        </Tooltip>
      ),
    },
    preBodyElement: (
      <>
        <AvatarCard
          containerProps={{ className: classes.infoCardWrapper }}
          noHover
          data={{
            title: candidate?.profile.fullName,
            subTitle: candidate?.profile.occupation?.label,
            image: candidate?.profile.croppedImageUrl,
          }}
        />
        <InitialValuesDisplay
          initialValues={initialValues}
          title="Currently Linked Jobs"
        />
      </>
    ),
    onSuccess() {
      onAlert(t('your_candidate_submitted_success'), 'success');
      Promise.all([
        queryClient.invalidateQueries({
          queryKey: [QueryKeys.getCandidateActivities, candidate?.id],
          exact: true,
        }),
        queryClient.invalidateQueries({
          queryKey: [QueryKeys.getCandidateRecruiterJobsList, candidate?.id],
          exact: true,
        }),
      ]);
    },
    onFailure(error) {
      onAlert(error.message ?? error.defaultMessage ?? 'Error!', 'error');
    },
    async apiFunc(data) {
      const jobIds = data.items.map((item: JobAPIProps) => item.id);
      console.log('jobIds', jobIds);

      if (!candidate?.id) {
        throw new Error('Candidate ID is required');
      }

      return linkJobsToCandidate(candidate.id, jobIds);
    },
    local: false,
  });
};
